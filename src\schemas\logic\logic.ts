import * as z from "zod";

const conditionSchema = z
  .object({
    id: z.string(),
    componentId: z.string().min(1, { message: "Enter parent question" }),
    operator: z.string().min(1, { message: "Enter operator" }),
    value: z.union([z.string(), z.number()]).optional(),
    minimumValue: z.union([z.string(), z.number()]).optional(),
    maximumValue: z.union([z.string(), z.number()]).optional(),
    logic: z.string().nullable(),
  })
  .superRefine((data, ctx) => {
    const addIssue = (field: string, message: string) => {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message,
        path: [field],
      });
    };

    const isEmpty = (value: any) => !value || value === "";
    const isRangeOperator = data.operator === "is in range";

    // Validate range operator fields
    if (isRangeOperator) {
      if (isEmpty(data.minimumValue)) addIssue("minimumValue", "Enter minimum value");
      if (isEmpty(data.maximumValue)) addIssue("maximumValue", "Enter maximum value");
      return;
    }

    // Validate single value field (for both set operators and empty operators to show immediate errors)
    if (isEmpty(data.value)) {
      addIssue("value", "Enter value");
    }
  });

const logicSchema = z.object({
  id: z.string(),
  conditions: z.array(conditionSchema).min(1, "At least one condition is required"),
  action: z.string().min(1, "Action is required"),
});

export const elementLogicSchema = z.object({
  logics: z.array(logicSchema).min(1, "At least one logic is required"),
});
